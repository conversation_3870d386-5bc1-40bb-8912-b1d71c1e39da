import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 从本地存储获取语言设置
    final prefs = await SharedPreferences.getInstance();
    String? language = prefs.getString("language") ?? "en";
    
    // 添加 accept-language 请求头
    options.headers['accept-language'] = language;
    
    handler.next(options);
  }
}
